export const requestStats = [
  {
    title: "Under Review",
    value: "15",
    icon: "Play",
    color: "amber",
  },
  {
    title: "Approved",
    value: "142",
    icon: "Check",
    color: "green",
  },
  {
    title: "Rejected",
    value: "7",
    icon: "AlertTriangle",
    color: "red",
  },
];

export const mockRequests = [
  {
    id: "#REQ-1001",
    clientName: "SME 1",
    clientSector: "Retail",
    years: 5,
    account_balance: 50000,
    inflow: 3500,
    outflow: 2000,
    amount: "KWD 50,000",
    status: "Under Review",
    submissionDate: "2025-07-15",
    decisionReport: {
      creditScore: 720,
      debtToIncomeRatio: "35%",
      annualRevenue: "$180,000",
      yearsInBusiness: 5,
      riskAssessment: "Medium",
      recommendation: "Review",
      notes:
          "Strong credit history and stable business revenue. Meets all standard criteria for business loan approval.",
    },
    reviewedBy: "",
  },
  {
    id: "#REQ-1002",
    clientName: "SME 2",
    clientSector: "Restaurant",
    years: 6,
    account_balance: 20000,
    inflow: 2000,
    outflow: 1500,
    amount: "KWD 10,000",
    status: "Approved",
    submissionDate: "2025-07-01",
    decisionReport: {
      creditScore: 850,
      debtToIncomeRatio: "28%",
      annualRevenue: "$95,000",
      yearsInBusiness: 6,
      riskAssessment: "Low",
      recommendation: "Approve",
      notes: "Good credit score.",
    },
    reviewedBy: "Admin1",
  },
  {
    id: "#REQ-1003",
    clientName: "SME 3",
    clientSector: "Entertainment",
    years: 3,
    account_balance: 40000,
    inflow: 2500,
    outflow: 2000,
    amount: "KWD 120,000",
    status: "rejected",
    submissionDate: "2025-07-03",
    decisionReport: {
      creditScore: 680,
      debtToIncomeRatio: "22%",
      annualRevenue: "$320,000",
      yearsInBusiness: 3,
      riskAssessment: "High",
      recommendation: "Reject",
      notes: "Very high risk, high amount",
    },
    reviewedBy: "Admin1",
  },
];
