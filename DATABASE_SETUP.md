# PostgreSQL Database Setup Guide

This guide will help you set up a local PostgreSQL database connection for your BackofficeManager application.

## Prerequisites

1. **PostgreSQL installed locally**
   - Download from: https://www.postgresql.org/download/
   - Or use a package manager like <PERSON><PERSON>: `choco install postgresql`

2. **Database created**
   - You'll need a database for the application to connect to

## Setup Steps

### 1. Configure Environment Variables

Edit the `.env` file in your project root and update the `DATABASE_URL`:

```env
DATABASE_URL="postgresql://username:password@localhost:5432/database_name"
```

**Example configurations:**

For default PostgreSQL installation:
```env
DATABASE_URL="postgresql://postgres:your_password@localhost:5432/backoffice_db"
```

For custom user:
```env
DATABASE_URL="postgresql://myuser:mypassword@localhost:5432/backoffice_db"
```

### 2. Create the Database

Connect to PostgreSQL and create your database:

```sql
-- Connect to PostgreSQL (replace 'postgres' with your username if different)
psql -U postgres

-- Create the database
CREATE DATABASE backoffice_db;

-- Exit psql
\q
```

### 3. Test the Connection

Run the database setup script to test your connection:

```bash
npm run db:setup
```

This will:
- Test your database connection
- Provide troubleshooting tips if connection fails

### 4. Push Database Schema

Once connected, push your schema to create the tables:

```bash
npm run db:push
```

This will create the `users` table and any other tables defined in your schema.

### 5. Start the Application

```bash
npm run dev
```

## Troubleshooting

### Connection Issues

1. **"Connection refused"**
   - Make sure PostgreSQL service is running
   - Check if PostgreSQL is listening on port 5432

2. **"Authentication failed"**
   - Verify your username and password in the DATABASE_URL
   - Check PostgreSQL's `pg_hba.conf` file for authentication settings

3. **"Database does not exist"**
   - Create the database using the SQL commands above
   - Make sure the database name in DATABASE_URL matches the created database

### Common PostgreSQL Commands

```sql
-- List all databases
\l

-- Connect to a specific database
\c database_name

-- List all tables in current database
\dt

-- Show table structure
\d table_name
```

## Database Schema

The application currently includes:

- **users** table: For user authentication
  - `id` (serial, primary key)
  - `username` (text, unique, not null)
  - `password` (text, not null)

## Development vs Production

- **Development**: Uses local PostgreSQL with the configuration in `.env`
- **Production**: Uses the DATABASE_URL environment variable from your hosting provider
- **Fallback**: If no DATABASE_URL is provided, the app uses in-memory storage (data is lost on restart)
