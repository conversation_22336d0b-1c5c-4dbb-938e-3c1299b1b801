import { Switch, Route } from "wouter"
import { queryClient } from "./lib/queryClient"
import { QueryClientProvider } from "@tanstack/react-query"
import { Toaster } from "@/components/ui/toaster"
import { TooltipProvider } from "@/components/ui/tooltip"
import NotFound from "@/pages/not-found"
import MainLayout from "@/components/layout/main-layout"
import Dashboard from "@/pages/dashboard"
import Clients from "@/pages/clients"
import ClientDetails from "@/pages/client-details"
import Requests from "@/pages/requests"

function Router() {
    return (
        <Switch>
            <Route path="/" component={Dashboard} />
            <Route path="/dashboard" component={Dashboard} />
            <Route path="/clients" component={Clients} />
            <Route path="/clients/:id" component={ClientDetails} />
            <Route path="/requests" component={Requests} />
            <Route component={NotFound} />
        </Switch>
    )
}

function App() {
    return (
        <QueryClientProvider client={queryClient}>
            <TooltipProvider>
                <Toaster />
                <MainLayout>
                    <Router />
                </MainLayout>
            </TooltipProvider>
        </QueryClientProvider>
    )
}

export default App
