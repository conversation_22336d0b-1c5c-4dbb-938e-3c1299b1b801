#!/usr/bin/env tsx

import { testConnection } from "../server/db-init.js";

async function main() {
  console.log("🔧 Setting up database connection...");
  
  // Check if DATABASE_URL is set
  if (!process.env.DATABASE_URL) {
    console.error("❌ DATABASE_URL environment variable is not set");
    console.log("\n📝 Please set your DATABASE_URL in the .env file:");
    console.log("DATABASE_URL=\"postgresql://username:password@localhost:5432/database_name\"");
    console.log("\nExample for local PostgreSQL:");
    console.log("DATABASE_URL=\"postgresql://postgres:password@localhost:5432/backoffice_db\"");
    process.exit(1);
  }

  console.log("🔍 Testing database connection...");
  const connected = await testConnection();
  
  if (connected) {
    console.log("✅ Database setup complete!");
    console.log("\n🚀 You can now run: npm run dev");
  } else {
    console.log("\n❌ Database setup failed!");
    console.log("\n🔧 Troubleshooting steps:");
    console.log("1. Make sure PostgreSQL is running on your system");
    console.log("2. Check your DATABASE_URL in the .env file");
    console.log("3. Ensure the database exists");
    console.log("4. Verify your username and password are correct");
    console.log("\n📚 To create a database in PostgreSQL:");
    console.log("psql -U postgres -c \"CREATE DATABASE backoffice_db;\"");
    process.exit(1);
  }
}

main().catch(console.error);
