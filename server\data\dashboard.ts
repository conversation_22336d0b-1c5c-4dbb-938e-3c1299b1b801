export const dashboardStats = [
  {
    title: "Total Clients",
    value: "1,247",
    change: "+12%",
    trend: "up",
    icon: "Users",
    color: "blue",
  },
  {
    title: "Active Requests",
    value: "89",
    change: "-3%",
    trend: "down",
    icon: "ClipboardList",
    color: "amber",
  },
  {
    title: "Approved Requests",
    value: "24",
    change: "+8%",
    trend: "up",
    icon: "CheckCircle",
    color: "green",
  },
  {
    title: "Finance Given",
    value: "KWD 54,290",
    change: "+15%",
    trend: "up",
    icon: "DollarSign",
    color: "purple",
  },
];

export const recentActivities = [
  {
    type: "user-plus",
    title: "New client registration",
    time: "2 minutes ago",
    color: "blue",
  },
  {
    type: "check",
    title: "Request #1234 completed",
    time: "5 minutes ago",
    color: "green",
  },
  {
    type: "exclamation",
    title: "High priority request assigned",
    time: "10 minutes ago",
    color: "amber",
  },
];

export const quickActions = [
  {
    title: "Add Client",
    icon: "UserPlus",
    color: "blue",
  },
  {
    title: "New Request",
    icon: "PlusCircle",
    color: "green",
  },
  {
    title: "Reports",
    icon: "BarChart3",
    color: "purple",
  },
  {
    title: "Settings",
    icon: "Settings",
    color: "gray",
  },
];
