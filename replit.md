# BackOffice Management Application

## Overview

This is a full-stack web application built with Express.js and React that provides a backoffice management interface for loan requests. The application features a dashboard with statistics, client management, and loan request tracking functionality. It uses a modern tech stack with TypeScript, Tailwind CSS, and shadcn/ui components for a polished user interface.

## User Preferences

Preferred communication style: Simple, everyday language.

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **Routing**: Wouter for lightweight client-side routing
- **State Management**: TanStack Query (React Query) for server state management
- **UI Framework**: shadcn/ui components built on Radix UI primitives
- **Styling**: Tailwind CSS with CSS variables for theming
- **Build Tool**: Vite for fast development and optimized builds

### Backend Architecture
- **Framework**: Express.js with TypeScript
- **Database**: PostgreSQL with Drizzle ORM
- **Database Provider**: Neon Database (serverless PostgreSQL)
- **Session Management**: Connect-pg-simple for PostgreSQL-backed sessions
- **Development**: Hot reloading with tsx for server-side development

### Data Storage Solutions
- **Primary Database**: PostgreSQL hosted on Neon Database
- **ORM**: Drizzle ORM with zod for type-safe database operations
- **Migration System**: Drizzle Kit for database schema migrations
- **Fallback Storage**: In-memory storage class for development/testing

## Key Components

### Frontend Components
1. **Layout System**: Main layout with responsive sidebar navigation
2. **Dashboard**: Statistics cards with metrics and quick actions
3. **Client Management**: Client listing, search, and filtering interface
4. **Loan Request Management**: Loan application tracking with status management and evaluation criteria popup
5. **UI Components**: Comprehensive shadcn/ui component library including dialog components for evaluation criteria

### Backend Components
1. **Storage Interface**: Abstracted storage layer with in-memory implementation
2. **Route Registration**: Modular route system for API endpoints
3. **Middleware**: Request logging and error handling
4. **Development Server**: Vite integration for seamless development

### Database Schema
- **Users Table**: Basic user management with username/password fields
- **Extensible Design**: Schema designed to be extended for clients and requests

## Data Flow

1. **Client Requests**: Browser makes requests to Express.js API endpoints
2. **Route Handling**: Express routes process requests and interact with storage layer
3. **Data Access**: Storage interface abstracts database operations via Drizzle ORM
4. **Response Formatting**: API returns JSON responses consumed by React frontend
5. **State Management**: TanStack Query handles caching and synchronization of server state
6. **UI Updates**: React components automatically re-render based on state changes

## External Dependencies

### Database & Infrastructure
- **Neon Database**: Serverless PostgreSQL hosting
- **Environment Variables**: DATABASE_URL for database connection

### Frontend Dependencies
- **Radix UI**: Comprehensive component primitives
- **TanStack Query**: Server state management
- **Wouter**: Lightweight routing
- **Tailwind CSS**: Utility-first styling
- **Lucide React**: Icon library

### Backend Dependencies
- **Drizzle ORM**: Type-safe database operations
- **Express.js**: Web application framework
- **Connect-pg-simple**: PostgreSQL session store

### Development Dependencies
- **Vite**: Build tool and development server
- **TypeScript**: Type safety across the stack
- **ESBuild**: Fast JavaScript bundling for production

## Deployment Strategy

### Build Process
1. **Frontend Build**: Vite builds React application to `dist/public`
2. **Backend Build**: ESBuild bundles server code to `dist/index.js`
3. **Database Setup**: Drizzle migrations ensure schema is up-to-date

### Environment Configuration
- **Development**: Uses tsx for hot reloading and Vite dev server
- **Production**: Serves built static files and compiled Express server
- **Database**: Requires DATABASE_URL environment variable for PostgreSQL connection

### Deployment Requirements
- Node.js environment with ES modules support
- PostgreSQL database (Neon Database recommended)
- Environment variables for database connection
- Static file serving capability for frontend assets

The application is designed for easy deployment to platforms like Replit, Vercel, or traditional VPS hosting with minimal configuration requirements.